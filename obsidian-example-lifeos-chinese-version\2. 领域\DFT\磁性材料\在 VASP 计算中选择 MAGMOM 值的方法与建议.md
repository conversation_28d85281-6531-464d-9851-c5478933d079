---
tags:
  - 领域/DFT
  - 磁性材料
---
**Course Name**:

## Definition and Function of `MAGMOM`

- **Basic Function**: `MAGMOM` is an input parameter in VASP **self-consistent field (SCF) spin-polarized calculations** that provides **initial local magnetic moments** for each atom when the **initial electron density lacks magnetization**. It is also used in determining system symmetry [vasp.at](https://www.vasp.at/wiki/index.php/MAGMOM#:~:text=,charges%20and%20forces). For non-spin-polarized calculations (`ISPIN=1`), `MAGMOM` has no effect.
    
- **Format**: For **collinear magnetic calculations** (`ISPIN=2`), `MAGMOM` is an array containing **NIONS real numbers**, where each value corresponds to one atom's initial magnetic moment. Positive and negative signs indicate spin direction [vasp.at](https://www.vasp.at/wiki/index.php/MAGMOM#:~:text=%2A%20For%20a%20spin,no%20orientation%20in%20the%20lattice). For **non-collinear calculations** (`LNONCOLLINEAR=.TRUE.`), each atom requires three components (`Mx My Mz`), with direction defined by `SAXIS` [vasp.at](https://www.vasp.at/wiki/index.php/MAGMOM#:~:text=,matrix%20or%20SAXIS%20is%20defined).
    
- **Default Value**: For `ISPIN=2`, VASP assigns a default value of 1 μB to each atom if not explicitly specified [vasp.at](https://www.vasp.at/wiki/index.php/MAGMOM#:~:text=MAGMOM%C2%A0%3D%20).
    
- **Initial Guess Only**: `MAGMOM` serves only as an initial magnetic moment guess. The actual magnetic moments are determined through self-consistent iteration. The VASP manual recommends using **1.2–1.5 times** experimental or known magnetic moments as initial values to facilitate convergence to the magnetic ground state [vasp.at](https://www.vasp.at/wiki/index.php/MAGMOM#:~:text=Tip%3A%20To%20converge%20to%20the,the%20procedure%20outlined%20in%20the). The VASP forum also suggests that verified or experimental magnetic moments multiplied by **1.3–1.5** provide excellent starting values
    
- **Symmetry Effects**: Setting `MAGMOM` reduces crystal symmetry [vasp.at](https://www.vasp.at/wiki/index.php/MAGMOM#:~:text=symmetry%20of%20the%20system%20,charges%20and%20forces). When restarting magnetic calculations, `MAGMOM` is used only for symmetry determination. If existing spin density is used (`ICHARG=1`), the initial magnetic moment information is read from `CHGCAR`/`WAVECAR` files [vasp.at](https://www.vasp.at/wiki/index.php/MAGMOM#:~:text=,likely%20to%20be%20symmetrized%20away).
    

## `MAGMOM` Selection for Common Systems

### Ferromagnetic Systems

1. **Principle**: All magnetic atoms should have consistently oriented initial magnetic moments, while non-magnetic atoms are set to 0. When selecting magnitudes, refer to experimental or theoretical magnetic moments for that element and appropriately amplify them (×1.2–1.5) to accelerate convergence [vasp.at](https://www.vasp.at/wiki/index.php/MAGMOM#:~:text=Tip%3A%20To%20converge%20to%20the,the%20procedure%20outlined%20in%20the)!. Final self-consistent results need not equal the initial values, but reasonable initial guesses help locate the correct magnetic state.
    
2. **Typical Values for Transition Metals**:
    

|Element|Free Ion/Solid Common Spin Number|Recommended Initial Magnetic Moment (μB/atom)|
|---|---|---|
|V|3d³ (high spin)|~3 (e.g., `MAGMOM=3`)|
|Cr|3d⁵|~5|
|Mn|3d⁵|~5|
|Fe|3d⁶, bcc Fe experimental moment ≈2.2 μB|Initial guess: 2.5–3|
|Co|3d⁷, magnetic moment ≈1.6 μB|Initial guess: ~2|
|Ni|3d⁸, fcc Ni moment ≈0.6 μB[nsc.liu.se](https://www.nsc.liu.se/support/Events/VASP_workshop_2020/fcc_Ni_rev/#:~:text=SYSTEM%20%20%3D%20Ni%20fcc,bulk)|Initial guess: ~1.0|

3. **Example**: A bcc Fe supercell containing 2 Fe atoms can be configured as:
    
    ```
    ISPIN = 2
    MAGMOM = 2*2.5   # 2 atoms each with 2.5 μB
    ```
    
    For systems containing magnetic dopant elements (such as rare earth elements) and a non-magnetic host lattice, specify them separately:
    
    ```
    MAGMOM = 4*0 2*4.0  # 4 Si atoms set to 0, 2 Eu atoms set to 4.0
    ```
    
4. **Verification**: After achieving self-consistency, verify local and total magnetic moments by examining `mag=` values in `OSZICAR` or the `magnetization (x)` sections in `OUTCAR` to confirm they meet expectations [nsc.liu.se](https://www.nsc.liu.se/support/Events/VASP_workshop_2020/fcc_Ni_rev/#:~:text=1%20F%3D%20,5874).
    

### Antiferromagnetic (AFM) and Ferromagnetic/Antiferromagnetic Mixed Systems

1. **Basic Approach**: Adjacent magnetic sublattices should have initial magnetic moments of equal magnitude but opposite signs, ensuring zero total magnetic moment. [vasp.at](https://www.vasp.at/wiki/index.php/MAGMOM#:~:text=,alignment%20would%20be%20the%20following) provides a simple bcc antiferromagnetic example:
    
    ```
    ISPIN = 2
    MAGMOM = 1.0 -1.0  # Two atoms with opposite spins
    ```
    
2. **Complex AFM Ordering**: For multi-atom unit cells (such as FeO, NiO, MnO), manually arrange the atom order in `POSCAR` to reflect the magnetic sublattice structure, then assign positive and negative values accordingly in `MAGMOM`. For example, in rock salt FeO where Fe atoms alternate, set `MAGMOM = 4*5 -4*5` (4 Fe atoms with +5, another 4 with –5).
    
3. **Testing Multiple AFM Orders**: Since VASP calculation results may depend on initial magnetic moments, calculate different AFM orderings separately and compare energies. **ShakeNBreak** recommends testing two equivalent AFM orderings by flipping all `MAGMOM` values to achieve spin reversal [shakenbreak.readthedocs.io](https://shakenbreak.readthedocs.io/en/latest/Tips.html#:~:text=If%20your%20host%20system%20is,to%20match%20this).
    
4. **Ferrimagnetic/Ferromagnetic Hybrids**: When systems contain different types of magnetic elements (such as ferrimagnets), each sublattice has different magnetic moment magnitudes, with signs according to the actual arrangement. For example, in CoFe₂O₄, Co²⁺ high spin ~3 μB, Fe³⁺ ~5 μB, set `MAGMOM = 1*3 2*5 -1*3 -2*5`.
    

### Paramagnetic/High-Temperature Disordered Magnetic States

1. **Paramagnetic Approximation**: Paramagnetic states lack long-range magnetism but possess local magnetic moments. Standard DFT cannot simulate completely random spin distributions. One approximation method maintains zero total spin (NUPDOWN=0) while assigning equal positive and negative initial magnetic moments to each magnetic atom, or randomly assigning ±M values, then averaging over multiple random configurations.
    
2. **Simple Implementation**: Set `ISPIN=2` and `NUPDOWN=0`, then randomly assign ±M to different atoms through `MAGMOM`:
    
    ```
    ISPIN  = 2
    NUPDOWN = 0
    MAGMOM = 1 -1 1 -1  ...  # Random arrangement ensuring sum equals 0
    ```
    
    After self-consistency, each atom's magnetic moment will decay or approach zero, requiring averaging over multiple random configurations.
    
3. **DLM Method**: For high-temperature paramagnets, the **Disordered Local Moments (DLM)** method automatically randomizes `MAGMOM` and periodically flips spins during molecular dynamics [arxiv.org](https://arxiv.org/html/2504.19184v2#:~:text=Report%20issue%20for%20preceding%20element,Automated%20AIMD%2BDLM%20Simulations%20in%20VASP). This method requires complex implementation with scripts to randomly set `MAGMOM` and perform averaging. For static calculations, using non-magnetic mode (`ISPIN=1`) is often more convenient.
    

### Non-magnetic Systems

1. **Non-magnetic Solids or Molecules**: For inherently non-magnetic materials, directly use `ISPIN=1` (non-spin-polarized), which requires no `MAGMOM` setting.
    
2. **Ensuring Accuracy**: When using `ISPIN=2` for code consistency, explicitly specify `MAGMOM=0` for all atoms. Otherwise, VASP defaults to 1 μB per atom, potentially causing spurious magnetization.
    

### Molecules and Small Systems

1. **Spin Multiplicity**: Molecular magnetism is typically determined by the total spin quantum number S, with `NUPDOWN`=2S. For example, O₂ in its ground state is a triplet (S=1), requiring `NUPDOWN=2` and `MAGMOM=1 1`. Ground state nitric oxide NO is a doublet (S=1/2), set with `NUPDOWN=1` and `MAGMOM=1 0`.
    
2. **Non-magnetic Molecules**: Molecules such as N₂ and CO₂ are singlets; use `ISPIN=1` or `MAGMOM=0 0`.
    

## Special Considerations for Elements or Systems

### 3d Transition Metals and Strongly Correlated Systems

- **Transition Metal Approximate Magnetic Moments**: The table above lists typical high-spin values. For 3d elements, estimate magnetic moments by counting unpaired d electrons (e.g., Mn²⁺ 3d⁵→5 μB). The Mat3ra tutorial suggests treating **V, Cr, Mn, Fe, Co, Ni** as ferromagnetic elements, alternately setting ±5 μB for them and 0 for other elements [docs.mat3ra.com](https://docs.mat3ra.com/tutorials/templating/set-magnetic-moment/#:~:text=In%20the%20second%20line%2C%20we,Ni). This "maximum initial guess" approach helps explore multiple magnetic configurations, but adjust magnitudes according to specific systems in actual calculations.
    
- **Strongly Correlated Systems**: Strongly correlated systems like MnO and NiO often require DFT+U, which is beyond this guide's scope. When choosing `MAGMOM`, provide slightly larger initial values based on U-corrected experimental magnetic moments (e.g., NiO ~1.7 μB → initial guess 2.0).
    

### Rare Earth 4f Elements and Strong Spin-Orbit Coupling

- **Non-collinear and SOC**: Rare earth or actinide elements exhibit significant spin-orbit coupling, requiring `LNONCOLLINEAR=.TRUE.` and `LSORBIT=.TRUE.`. In this case, `MAGMOM` must be specified as three-component vectors, consistent with the spin quantization axis defined by `SAXIS` [vasp.at](https://www.vasp.at/wiki/index.php/MAGMOM#:~:text=,matrix%20or%20SAXIS%20is%20defined). For example, Gd³⁺ (4f⁷) can be set as `MAGMOM = 0 0 7`; if the easy axis is along [100], use `MAGMOM = 7 0 0`.
    
- **Local Angular Momentum Direction**: For systems with strong anisotropy, test multiple directions in preliminary calculations, examining total energy minimization results to determine the magnetization easy axis.
    

## Common Errors and Avoidance Methods

1. **`MAGMOM` Count Mismatch with Atom Number**: The number of array elements must equal the number of atoms or use multiplication expansion format, such as `3*0 28*0.9`. Mathematical expressions (like `28*0.6*1.3`) are not allowed, as VASP only recognizes multiplication expansion [ww.vasp.at](https://ww.vasp.at/forum/viewtopic.php#:~:text=28,can%20give%20each%20moment%20explicitely).
    
2. **Forgetting to Delete Old `CHGCAR`/`WAVECAR`**: `MAGMOM` only takes effect when no spin density exists. When using existing spin density (such as restarting calculations), delete `CHGCAR` or use `ICHARG=2` to make the new `MAGMOM` effective [vasp.at](https://www.vasp.at/wiki/index.php/MAGMOM#:~:text=,likely%20to%20be%20symmetrized%20away).
    
3. **Incorrectly Set AFM Ordering**: If the atom order in `POSCAR` doesn't match the actual antiferromagnetic arrangement, the system may converge to a ferromagnetic state even with `MAGMOM` set. Rearrange atom order according to the magnetic structure.
    
4. **Ignoring Total Spin Constraints**: For molecules or finite systems, apply `NUPDOWN` to constrain total spin. Without this setting, VASP automatically determines total magnetic moment based on `MAGMOM`, potentially yielding unexpected spin states.
    
5. **Convergence Difficulties**: Initial magnetic moments that are too small or incorrectly oriented may trap the energy in local minima. Try increasing initial magnetic moments (1.3–1.5 times experimental values) [vasp.at](https://www.vasp.at/wiki/index.php/MAGMOM#:~:text=Tip%3A%20To%20converge%20to%20the,the%20procedure%20outlined%20in%20the)![](https://sdmntprnorthcentralus.oaiusercontent.com/files/00000000-a818-622f-9770-f1954056bb1a/raw?se=2025-08-14T03%3A38%3A11Z&sp=r&sv=2024-08-04&sr=b&scid=868a47c8-95a1-56f9-a4bc-62c4ef0a9072&skoid=bbd22fc4-f881-4ea4-b2f3-c12033cf6a8b&sktid=a48cca56-e6da-484e-a814-9c849652bcb3&skt=2025-08-13T19%3A57%3A42Z&ske=2025-08-14T19%3A57%3A42Z&sks=b&skv=2024-08-04&sig=edkgi8BCyupp6dxV8I7Wyl4/igGirEnE6AnLsOXcZw8%3D)vasp.at or first perform non-magnetic calculations before enabling spin polarization.
    
6. **Overfitting MAGMOM**: `MAGMOM` is only an initial guess and does not represent actual magnetic moments. When comparing different magnetic states, always use self-consistent energies and final magnetic moments as criteria.
    

## Recommended Verification and Iteration Steps

1. **Initial Exploration**:
    
    - Begin with a non-magnetic structure and perform several relaxation steps (`ISPIN=1`) to obtain reasonable geometry. Then switch to spin-polarized mode (`ISPIN=2`), set `MAGMOM`, and continue calculations to improve convergence. [vasp.at](https://www.vasp.at/wiki/index.php/MAGMOM#:~:text=,likely%20to%20be%20symmetrized%20away) suggests that restarting from a non-magnetic state can enhance convergence.
        
    - For unknown magnetic materials, simultaneously test multiple initial magnetic moments (such as 0, expected moment, oversized values) and compare energies to identify the lowest energy state. The Ni fcc tutorial example suggests trying `MAGMOM=0.0`, `1.0`, `2.0` [nsc.liu.se](https://www.nsc.liu.se/support/Events/VASP_workshop_2020/fcc_Ni_rev/#:~:text=,JOBID.out).
        
2. **Check Local Magnetic Moments**: After achieving self-consistency, examine the "magnetization (x)" table in `OUTCAR` to view s/p/d/f partial wave magnetic moments for each atom [nsc.liu.se](https://www.nsc.liu.se/support/Events/VASP_workshop_2020/fcc_Ni_rev/#:~:text=1%20F%3D%20,5874).
    
3. **Energy Comparison**: If multiple magnetic configurations converge, compare total energies to determine the ground state. For example, if the energy difference between FM and AFM states is very small, use finer k-point meshes and higher precision for reconfirmation.
    
4. **Test Magnetization Directions**: For systems with strong spin-orbit coupling, test different `MAGMOM` directions and compare energy differences to determine the easy magnetic axis. Results can be compared with experimental anisotropy energies.
    
5. **Use Constraints**: To obtain specific magnetic moments regardless of self-consistent convergence results, use the `I_CONSTRAINED_M` tag to constrain magnetic moments [vasp.at](https://www.vasp.at/wiki/index.php/MAGMOM#:~:text=The%20I_CONSTRAINED_M%20tag%20can%20constrain,site%20magnetic%20moments) (detailed discussion beyond this guide's scope).
    

## Summary

The core principle of choosing `MAGMOM` in VASP is to provide appropriate initial magnetic moments for each magnetic atom to induce the desired magnetic configuration, while recognizing that `MAGMOM` serves only as an initial guess. Ferromagnetic systems require same-direction magnetic moments for all magnetic atoms, while antiferromagnetic systems assign opposite signs to different sublattices. Initial values should be slightly larger than experimental or theoretical magnetic moments (typically multiply by 1.2–1.5) [vasp.at](https://www.vasp.at/wiki/index.php/MAGMOM#:~:text=Tip%3A%20To%20converge%20to%20the,the%20procedure%20outlined%20in%20the)![](https://sdmntprnorthcentralus.oaiusercontent.com/files/00000000-a818-622f-9770-f1954056bb1a/raw?se=2025-08-14T03%3A38%3A11Z&sp=r&sv=2024-08-04&sr=b&scid=868a47c8-95a1-56f9-a4bc-62c4ef0a9072&skoid=bbd22fc4-f881-4ea4-b2f3-c12033cf6a8b&sktid=a48cca56-e6da-484e-a814-9c849652bcb3&skt=2025-08-13T19%3A57%3A42Z&ske=2025-08-14T19%3A57%3A42Z&sks=b&skv=2024-08-04&sig=edkgi8BCyupp6dxV8I7Wyl4/igGirEnE6AnLsOXcZw8%3D)vasp.at. For paramagnetic/non-magnetic systems, use `ISPIN=1` or set all `MAGMOM` values to zero. For non-collinear or strong spin-orbit coupling materials, provide vector-form `MAGMOM` and specify the spin axis. In practice, by testing different initial values, checking final magnetic moments, and comparing energies, one can reliably determine the magnetic ground state of a system.