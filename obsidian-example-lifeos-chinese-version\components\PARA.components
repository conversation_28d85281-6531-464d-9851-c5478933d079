{"components": [{"id": "875a936e-78c5-4888-b0fc-f9401ae410fe", "type": "multi", "titleAlign": "center", "tabTitle": "", "maxWidthRatio": -1, "showBorder": true, "showShadow": false, "createAt": "2025-01-02T08:35:11.302Z", "updateAt": "2025-01-02T08:35:11.302Z", "components": [{"componentId": "02e7ffb2-1f89-4da5-a611-8a8da65a9507"}, {"componentId": "7a6147d7-f673-47fc-bc46-3d6b13bb48df"}, {"componentId": "d7b3ac56-b85f-4bae-a9f6-648848f61b76"}, {"componentId": "6ea800bc-d855-4040-b0a7-094b26ac13a2"}], "layoutType": "tab", "locked": false, "layoutOptions": {}}, {"id": "02e7ffb2-1f89-4da5-a611-8a8da65a9507", "type": "dynamicDataView", "titleAlign": "center", "tabTitle": "project", "maxWidthRatio": -1, "showBorder": false, "showShadow": false, "createAt": "2025-01-02T08:35:54.588Z", "updateAt": "2025-01-02T08:35:54.588Z", "viewType": "table", "newPageNameFormat": "{{date:YYYYMMDDHHmmss}} ", "properties": [{"id": "__componentsTitleProperty_0x7c00", "name": "File Basename", "type": "text", "isShow": true, "wrap": false, "options": {}}, {"id": "79f446d6-b659-4f42-b627-0ab9f8a29b99", "name": "tags", "isShow": true, "type": "multiSelect", "options": {}}, {"id": "277b539f-8a6f-4ef1-8218-6a134faf1949", "name": "ai总结", "isShow": true, "type": "button", "options": {"pinned": null, "action": {"type": "runScript", "properties": [], "expression": "aiSummary(\"sk-c4a121dd9efc4bd6ab3233872f35476a\",\"总结\")"}}}, {"id": "7ecceae9-19aa-496a-a29f-482649ff0e2b", "name": "总结", "isShow": true, "type": "text", "options": {"width": "533", "pinned": null}, "wrap": true}], "templates": [], "groups": [{"id": "4e8e364e-43ea-43d5-ae11-e8b5331a5371", "name": "项目/申博", "items": [], "collapsed": true}, {"id": "9bb25ee4-7919-4bf4-a280-f0ea4d4d1eb5", "name": "项目/雅思", "items": [], "collapsed": true}, {"id": "cf58c650-d5fb-432b-88a6-2f5bbc95549a", "name": "项目/间隙预测", "items": [], "collapsed": true}], "colorfulGroups": true, "viewOptions": {"openPageIn": "tab", "openPageAfterCreate": true, "items": [], "showGrid": false, "heightType": "auto", "heightValue": 600, "pinFiltersToMenuBar": true}, "loadLimitPerPage": 10, "filter": {"id": "ed11f8e3-e8f5-487b-90d9-21c5e0ce693a", "type": "group", "operator": "and", "conditions": [{"id": "7ef0c7ad-5402-43aa-9816-5d6864cff078", "type": "filter", "operator": "contains_any", "property": "tags", "value": "项目", "conditions": []}, {"id": "5cc7088c-19af-4d97-ad4e-065e85ce3a24", "type": "filter", "operator": "contains", "property": "database-plugin", "value": "basic", "conditions": []}]}, "groupBy": "tags", "groupStates": {"collapseds": ["材料学", "回归", "机器学习", "基础文献", "奖学金", "脚本", "论文", "文献笔记", "雅思口语", "雅思听力", "DFT", "IELTS-Listening", "IELTS/reading", "IELTS/speaking", "phd-meethings", "RP写作", "__$uncategorizedGroup", "course", "项目/雅思", "项目/AI写论文", "项目/间隙预测", "项目/申博"], "hiddens": ["__$uncategorizedGroup", "RP写作", "phd-meethings", "IELTS/reading", "IELTS-Listening", "course", "雅思口语"]}}, {"id": "7a6147d7-f673-47fc-bc46-3d6b13bb48df", "type": "dynamicDataView", "titleAlign": "center", "tabTitle": "area", "maxWidthRatio": -1, "showBorder": false, "showShadow": false, "createAt": "2025-01-02T08:45:20.369Z", "updateAt": "2025-01-02T08:45:20.369Z", "viewType": "table", "newPageNameFormat": "{{date:YYYYMMDDHHmmss}} ", "properties": [{"id": "__componentsTitleProperty_0x7c00", "name": "File Basename", "type": "text", "isShow": true, "wrap": false, "options": {}}], "templates": [], "groups": [{"id": "83be68a9-04c5-4155-a7d5-60d9c7d3df46", "name": "人际交往", "items": [], "collapsed": false}, {"id": "74dcb784-e8a3-4f3b-a34b-a4cb31ef6199", "name": "AI-for-science", "items": [], "collapsed": false}, {"id": "0a91c7f0-d627-4425-aafd-cecaacc489a5", "name": "DFT", "items": [], "collapsed": false}], "colorfulGroups": true, "viewOptions": {"openPageIn": "tab", "openPageAfterCreate": true, "items": [], "showGrid": false, "heightType": "auto", "heightValue": 600}, "groupBy": "tags"}, {"id": "d7b3ac56-b85f-4bae-a9f6-648848f61b76", "type": "dynamicDataView", "titleAlign": "center", "tabTitle": "resource", "maxWidthRatio": -1, "showBorder": false, "showShadow": false, "createAt": "2025-01-03T06:45:27.907Z", "updateAt": "2025-01-03T06:45:28.047Z", "viewType": "table", "newPageNameFormat": "{{date:YYYYMMDDHHmmss}} ", "properties": [{"id": "__componentsTitleProperty_0x7c00", "name": "File Basename", "type": "text", "isShow": true, "wrap": false, "options": {"width": "364"}}, {"id": "c967ba84-6c07-496a-b85c-d07a6a0eea97", "name": "tags", "isShow": true, "type": "multiSelect", "options": {}}, {"id": "1c191cf1-55b7-4083-a998-656e43c8dc7b", "name": "${file.path}", "isShow": true, "type": "text", "options": {"width": "449"}}], "templates": [], "groups": [], "colorfulGroups": true, "viewOptions": {"openPageIn": "tab", "openPageAfterCreate": true, "items": [], "showGrid": false, "heightType": "auto", "heightValue": 600}, "filter": {"id": "ed11f8e3-e8f5-487b-90d9-21c5e0ce693a", "type": "group", "operator": "and", "conditions": [{"id": "c488ed7e-f7f4-4d9b-8197-f08edda6d18e", "type": "filter", "operator": "contains", "property": "${file.tags}", "value": "#资源", "conditions": []}, {"id": "04c7d704-739d-4f79-8788-992fb6caeeae", "type": "filter", "operator": "contains", "property": "${file.path}", "value": "3.资源", "conditions": []}]}, "sort": {"orders": []}}, {"id": "6ea800bc-d855-4040-b0a7-094b26ac13a2", "type": "dynamicDataView", "titleAlign": "center", "tabTitle": "archive", "maxWidthRatio": -1, "showBorder": false, "showShadow": false, "createAt": "2025-01-04T13:30:20.689Z", "updateAt": "2025-01-04T13:30:20.689Z", "viewType": "table", "newPageNameFormat": "{{date:YYYYMMDDHHmmss}} ", "properties": [{"id": "__componentsTitleProperty_0x7c00", "name": "File Basename", "type": "text", "isShow": true, "wrap": false, "options": {}}], "templates": [], "groups": [], "colorfulGroups": false, "viewOptions": {"openPageIn": "tab", "openPageAfterCreate": true, "items": [], "showGrid": false, "heightType": "auto", "heightValue": 600}, "filter": {"id": "68fcbf35-c9f1-47b5-8fc2-38f3aa688c88", "type": "group", "operator": "and", "conditions": [{"id": "6d08e8c5-1085-452f-8e51-9a7d4e0db173", "type": "filter", "operator": "contains", "property": "tags", "value": null, "conditions": []}, {"id": "73ca80b6-61a0-4bbd-9db0-f86e513bba2b", "type": "filter", "operator": "contains", "property": "${file.path}", "value": "4. 存档", "conditions": []}]}}], "rootComponentId": "875a936e-78c5-4888-b0fc-f9401ae410fe"}